'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeft,
  Users,
  Settings,
  BarChart3,
  Shield,
  Database,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  UserPlus,
  FileText
} from 'lucide-react';

export default function AdminPanel() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState({
    totalUsers: 156,
    activeUsers: 89,
    totalProjects: 23,
    systemHealth: 98
  });

  useEffect(() => {
    if (status === 'loading') return;

    // Check if user is authenticated and has admin email
    if (!session) {
      router.push('/');
      return;
    }

    // Check if user has admin privileges
    if (session.user?.email !== '<EMAIL>') {
      router.push('/');
      return;
    }
  }, [session, status, router]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-white text-lg">Yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!session || session.user?.email !== '<EMAIL>') {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16 max-w-3xl mx-auto">
          <Link
            href="/user-panel"
            className="inline-flex items-center gap-2 text-neutral-400 hover:text-white transition-colors mb-6 group"
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
            Kullanıcı Paneline Dön
          </Link>
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight text-white mb-4">
            Admin Paneli
          </h1>
          <p className="text-lg text-neutral-300">Sistem yönetimi ve kullanıcı kontrolü</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
          {/* Total Users Card */}
          <div className="bg-card text-card-foreground gap-6 rounded-xl border py-8 shadow-sm group flex flex-col transition-all duration-300 hover:shadow-lg hover:border-primary hover:-translate-y-1">
            <div className="px-8 flex flex-col items-center text-center">
              <div className="p-4 bg-gradient-to-br from-[#478cbf] to-[#2d5aa0] rounded-2xl text-white mb-6">
                <Users className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">{stats.totalUsers}</h3>
              <p className="text-neutral-300">Toplam Kullanıcı</p>
              <div className="flex items-center gap-2 mt-2">
                <TrendingUp className="w-4 h-4 text-green-400" />
                <span className="text-green-400 text-sm">+12% bu ay</span>
              </div>
            </div>
          </div>

          {/* Active Users Card */}
          <div className="bg-card text-card-foreground gap-6 rounded-xl border py-8 shadow-sm group flex flex-col transition-all duration-300 hover:shadow-lg hover:border-primary hover:-translate-y-1">
            <div className="px-8 flex flex-col items-center text-center">
              <div className="p-4 bg-gradient-to-br from-[#3b82f6] to-[#1d4ed8] rounded-2xl text-white mb-6">
                <Activity className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">{stats.activeUsers}</h3>
              <p className="text-neutral-300">Aktif Kullanıcı</p>
              <div className="flex items-center gap-2 mt-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span className="text-green-400 text-sm">Online</span>
              </div>
            </div>
          </div>

          {/* Total Projects Card */}
          <div className="bg-card text-card-foreground gap-6 rounded-xl border py-8 shadow-sm group flex flex-col transition-all duration-300 hover:shadow-lg hover:border-primary hover:-translate-y-1">
            <div className="px-8 flex flex-col items-center text-center">
              <div className="p-4 bg-gradient-to-br from-[#06b6d4] to-[#0ea5e9] rounded-2xl text-white mb-6">
                <FileText className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">{stats.totalProjects}</h3>
              <p className="text-neutral-300">Toplam Proje</p>
              <div className="flex items-center gap-2 mt-2">
                <Clock className="w-4 h-4 text-blue-400" />
                <span className="text-blue-400 text-sm">5 aktif</span>
              </div>
            </div>
          </div>

          {/* System Health Card */}
          <div className="bg-card text-card-foreground gap-6 rounded-xl border py-8 shadow-sm group flex flex-col transition-all duration-300 hover:shadow-lg hover:border-primary hover:-translate-y-1">
            <div className="px-8 flex flex-col items-center text-center">
              <div className="p-4 bg-gradient-to-br from-[#10b981] to-[#059669] rounded-2xl text-white mb-6">
                <Shield className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">{stats.systemHealth}%</h3>
              <p className="text-neutral-300">Sistem Sağlığı</p>
              <div className="flex items-center gap-2 mt-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span className="text-green-400 text-sm">Mükemmel</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
