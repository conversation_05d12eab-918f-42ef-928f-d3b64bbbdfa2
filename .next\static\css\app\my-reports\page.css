/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/styles/pages.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* Pages CSS - Ana sayfaya uyumlu stil dosyası */

/* Ana container stilleri */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  position: relative;
  overflow-x: hidden;
}

.page-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.page-content {
  position: relative;
  z-index: 1;
  padding: 2rem 1rem;
}

/* Header stilleri */
.page-header {
  margin-bottom: 2rem;
}

.page-back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #a3a3a3;
  text-decoration: none;
  transition: color 0.3s ease;
  margin-bottom: 1rem;
}

.page-back-link:hover {
  color: #ffffff;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #a3a3a3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  color: #a3a3a3;
  font-size: 1.1rem;
}

/* Card stilleri */
.page-card {
  background: rgba(23, 23, 23, 0.8);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(64, 64, 64, 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.page-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.5), transparent);
}

.page-card:hover {
  border-color: rgba(120, 119, 198, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.page-card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.page-card-icon {
  padding: 0.5rem;
  background: rgba(37, 99, 235, 0.2);
  border-radius: 0.5rem;
  color: #60a5fa;
}

.page-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
}

/* Form stilleri */
.page-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.page-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.page-form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #d4d4d4;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(38, 38, 38, 0.8);
  border: 1px solid rgba(64, 64, 64, 0.5);
  border-radius: 0.5rem;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.page-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.page-form-input::placeholder {
  color: #737373;
}

.page-form-textarea {
  resize: none;
  min-height: 120px;
}

.page-form-select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Button stilleri */
.page-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  text-decoration: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.page-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.page-btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.page-btn-secondary {
  background: rgba(64, 64, 64, 0.5);
  color: #d4d4d4;
  border: 1px solid rgba(64, 64, 64, 0.5);
}

.page-btn-secondary:hover {
  background: rgba(64, 64, 64, 0.8);
  color: #ffffff;
}

.page-btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
}

.page-btn-success:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.page-btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #ffffff;
}

.page-btn-danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Toggle switch stilleri */
.page-toggle {
  position: relative;
  display: inline-flex;
  height: 1.5rem;
  width: 2.75rem;
  align-items: center;
  border-radius: 9999px;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.page-toggle-active {
  background-color: #3b82f6;
}

.page-toggle-inactive {
  background-color: #404040;
}

.page-toggle-thumb {
  display: inline-block;
  height: 1rem;
  width: 1rem;
  transform: translateX(0.25rem);
  border-radius: 9999px;
  background-color: #ffffff;
  transition: transform 0.3s ease;
}

.page-toggle-thumb-active {
  transform: translateX(1.5rem);
}

/* Badge stilleri */
.page-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid;
}

.page-badge-success {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.page-badge-warning {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.page-badge-error {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.page-badge-info {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

/* Message stilleri */
.page-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid;
  margin-bottom: 1.5rem;
}

.page-message-success {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.page-message-error {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

/* Stats card stilleri */
.page-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-gap: 1.5rem;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.page-stat-card {
  background: rgba(23, 23, 23, 0.8);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(64, 64, 64, 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.page-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.5), transparent);
}

.page-stat-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-stat-icon {
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.page-stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
}

.page-stat-label {
  font-size: 0.875rem;
  color: #a3a3a3;
}

/* Tab stilleri */
.page-tabs {
  display: flex;
  gap: 0.25rem;
  background: rgba(23, 23, 23, 0.5);
  padding: 0.25rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
}

.page-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  background: none;
  color: #a3a3a3;
}

.page-tab-active {
  background: #3b82f6;
  color: #ffffff;
}

.page-tab:hover:not(.page-tab-active) {
  color: #ffffff;
  background: rgba(64, 64, 64, 0.5);
}

/* Filter stilleri */
.page-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.page-filter-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  background: rgba(64, 64, 64, 0.5);
  color: #d4d4d4;
}

.page-filter-btn-active {
  background: #3b82f6;
  color: #ffffff;
}

.page-filter-btn:hover:not(.page-filter-btn-active) {
  background: rgba(64, 64, 64, 0.8);
  color: #ffffff;
}

/* Loading spinner */
.page-spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .page-content {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .page-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .page-tabs {
    flex-direction: column;
  }
  
  .page-filters {
    justify-content: center;
  }
}

/* Hover effects */
.page-hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.page-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

/* Gradient text */
.page-gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass effect */
.page-glass {
  background: rgba(23, 23, 23, 0.8);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(64, 64, 64, 0.3);
}

/* Grid layouts */
.page-grid {
  display: grid;
  grid-gap: 1.5rem;
  gap: 1.5rem;
}

.page-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.page-grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.page-grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* List styles */
.page-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.page-list-item {
  display: flex;
  align-items: center;
  justify-content: between;
  padding: 1rem;
  background: rgba(38, 38, 38, 0.5);
  border: 1px solid rgba(64, 64, 64, 0.3);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.page-list-item:hover {
  background: rgba(38, 38, 38, 0.8);
  border-color: rgba(120, 119, 198, 0.5);
}

/* Progress bar */
.page-progress {
  width: 100%;
  height: 0.5rem;
  background: rgba(64, 64, 64, 0.5);
  border-radius: 9999px;
  overflow: hidden;
}

.page-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 9999px;
  transition: width 0.3s ease;
}

/* Skeleton loading */
.page-skeleton {
  background: linear-gradient(90deg, rgba(64, 64, 64, 0.3) 25%, rgba(64, 64, 64, 0.5) 50%, rgba(64, 64, 64, 0.3) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 0.5rem;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Tooltip */
.page-tooltip {
  position: relative;
  display: inline-block;
}

.page-tooltip-content {
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(23, 23, 23, 0.95);
  color: #ffffff;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  border: 1px solid rgba(64, 64, 64, 0.5);
}

.page-tooltip:hover .page-tooltip-content {
  opacity: 1;
  visibility: visible;
}

/* Modal overlay */
.page-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.page-modal {
  background: rgba(23, 23, 23, 0.95);
  border: 1px solid rgba(64, 64, 64, 0.5);
  border-radius: 1rem;
  padding: 2rem;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

/* Animations */
.page-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.page-slide-up {
  animation: slideUp 0.3s ease-out;
}

.page-scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility classes */
.page-text-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.page-border-gradient {
  position: relative;
  background: rgba(23, 23, 23, 0.8);
  border-radius: 1rem;
}

.page-border-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.5), rgba(139, 92, 246, 0.5));
  border-radius: inherit;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
          mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
          mask-composite: exclude;
}

