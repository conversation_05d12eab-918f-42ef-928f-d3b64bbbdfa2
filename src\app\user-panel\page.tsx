'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  User,
  Mail,
  Phone,
  Calendar,
  Shield,
  Wallet,
  Edit3,
  Save,
  X,
  ArrowLeft,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface UserData {
  id: string;
  username: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  balance: number;
  isVerified: boolean;
  createdAt: string;
}

export default function UserPanel() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setSaving] = useState(false);
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
  
  const [editForm, setEditForm] = useState({
    name: ''
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
    }
  }, [status, router]);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (!session?.user?.email) return;
      
      try {
        const response = await fetch('/api/users');
        if (response.ok) {
          const data = await response.json();
          setUserData(data);
          setEditForm({
            name: data.name || ''
          });
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (session) {
      fetchUserData();
    }
  }, [session]);

  const handleSave = async () => {
    setSaving(true);
    setMessage(null);
    
    try {
      const response = await fetch('/api/users', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (response.ok) {
        const updatedData = await response.json();
        setUserData(updatedData);
        setIsEditing(false);
        setMessage({ type: 'success', text: 'Bilgileriniz başarıyla güncellendi!' });
      } else {
        setMessage({ type: 'error', text: 'Güncelleme sırasında bir hata oluştu.' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Bir hata oluştu.' });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditForm({
      name: userData?.name || ''
    });
    setMessage(null);
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || !userData) {
    return null;
  }

  return (
    <div className="min-h-screen" style={{
      background: 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%)',
      position: 'relative'
    }}>
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors mb-4 group"
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
            Ana Sayfaya Dön
          </Link>
          <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-600 bg-clip-text text-transparent">
            Kullanıcı Paneli
          </h1>
          <p className="text-gray-400 text-lg">Hesap bilgilerinizi görüntüleyin ve düzenleyin</p>
        </div>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-xl border backdrop-blur-sm flex items-center gap-3 ${
            message.type === 'success'
              ? 'bg-green-500/10 border-green-500/30 text-green-400'
              : 'bg-red-500/10 border-red-500/30 text-red-400'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            {message.text}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-2">
            <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-white flex items-center gap-2">
                  <User className="w-5 h-5 text-blue-400" />
                  Profil Bilgileri
                </h2>
                {!isEditing ? (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-blue-500/25"
                  >
                    <Edit3 className="w-4 h-4" />
                    Düzenle
                  </button>
                ) : (
                  <div className="flex gap-2">
                    <button
                      onClick={handleSave}
                      disabled={isSaving}
                      className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-green-500/25"
                    >
                      <Save className="w-4 h-4" />
                      {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
                    </button>
                    <button
                      onClick={handleCancel}
                      className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-lg transition-all duration-300 shadow-lg"
                    >
                      <X className="w-4 h-4" />
                      İptal
                    </button>
                  </div>
                )}
              </div>

              <div className="space-y-6">
                {/* Name */}
                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-2">
                    <User className="w-4 h-4 inline mr-2" />
                    Ad Soyad
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editForm.name}
                      onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                      className="w-full px-4 py-3 bg-neutral-800 border border-neutral-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                    />
                  ) : (
                    <p className="text-white text-lg">{userData.name}</p>
                  )}
                </div>

                {/* Username - Read Only */}
                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-2">
                    <User className="w-4 h-4 inline mr-2" />
                    Kullanıcı Adı
                  </label>
                  <p className="text-white text-lg">{userData.username}</p>
                  <p className="text-xs text-neutral-500 mt-1">Kullanıcı adı değiştirilemez</p>
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-2">
                    <Mail className="w-4 h-4 inline mr-2" />
                    E-posta
                  </label>
                  <p className="text-white text-lg">{userData.email}</p>
                  <p className="text-xs text-neutral-500 mt-1">E-posta adresi değiştirilemez</p>
                </div>

                {/* Phone - Read Only */}
                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-2">
                    <Phone className="w-4 h-4 inline mr-2" />
                    Telefon
                  </label>
                  <p className="text-white text-lg">{userData.phone || 'Belirtilmemiş'}</p>
                  <p className="text-xs text-neutral-500 mt-1">Telefon numarası değiştirilemez</p>
                </div>
              </div>
            </div>
          </div>

          {/* Account Info */}
          <div className="space-y-6">
            {/* Account Status */}
            <div className="bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Hesap Durumu</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-neutral-300">Doğrulama</span>
                  <span className={`flex items-center gap-2 ${
                    userData.isVerified ? 'text-green-400' : 'text-yellow-400'
                  }`}>
                    <CheckCircle className="w-4 h-4" />
                    {userData.isVerified ? 'Doğrulanmış' : 'Beklemede'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-neutral-300">Rol</span>
                  <span className="flex items-center gap-2 text-blue-400">
                    <Shield className="w-4 h-4" />
                    {userData.role === 'ADMIN' ? 'Admin' : 'Kullanıcı'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-neutral-300">Bakiye</span>
                  <span className="flex items-center gap-2 text-green-400">
                    <Wallet className="w-4 h-4" />
                    ₺{userData.balance.toFixed(2)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-neutral-300">Üyelik Tarihi</span>
                  <span className="flex items-center gap-2 text-neutral-400">
                    <Calendar className="w-4 h-4" />
                    {new Date(userData.createdAt).toLocaleDateString('tr-TR')}
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Hızlı İşlemler</h3>
              <div className="space-y-3">
                <Link 
                  href="/settings" 
                  className="block w-full px-4 py-3 bg-neutral-800 hover:bg-neutral-700 text-white rounded-lg transition-colors text-center"
                >
                  Ayarlar
                </Link>
                <Link 
                  href="/my-reports" 
                  className="block w-full px-4 py-3 bg-neutral-800 hover:bg-neutral-700 text-white rounded-lg transition-colors text-center"
                >
                  Raporlarım
                </Link>
                <Link 
                  href="/support" 
                  className="block w-full px-4 py-3 bg-neutral-800 hover:bg-neutral-700 text-white rounded-lg transition-colors text-center"
                >
                  Destek
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
