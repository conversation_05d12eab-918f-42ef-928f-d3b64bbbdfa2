'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  User,
  Mail,
  Phone,
  Calendar,
  Shield,
  Wallet,
  Edit3,
  Save,
  X,
  ArrowLeft,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface UserData {
  id: string;
  username: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  balance: number;
  isVerified: boolean;
  createdAt: string;
}

export default function UserPanel() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setSaving] = useState(false);
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
  
  const [editForm, setEditForm] = useState({
    name: ''
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
    }
  }, [status, router]);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (!session?.user?.email) return;
      
      try {
        const response = await fetch('/api/users');
        if (response.ok) {
          const data = await response.json();
          setUserData(data);
          setEditForm({
            name: data.name || ''
          });
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (session) {
      fetchUserData();
    }
  }, [session]);

  const handleSave = async () => {
    setSaving(true);
    setMessage(null);
    
    try {
      const response = await fetch('/api/users', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (response.ok) {
        const updatedData = await response.json();
        setUserData(updatedData);
        setIsEditing(false);
        setMessage({ type: 'success', text: 'Bilgileriniz başarıyla güncellendi!' });
      } else {
        setMessage({ type: 'error', text: 'Güncelleme sırasında bir hata oluştu.' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Bir hata oluştu.' });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditForm({
      name: userData?.name || ''
    });
    setMessage(null);
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || !userData) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16 max-w-3xl mx-auto">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-neutral-400 hover:text-white transition-colors mb-6 group"
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
            Ana Sayfaya Dön
          </Link>
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight text-white mb-4">
            Kullanıcı Paneli
          </h1>
          <p className="text-lg text-neutral-300">Hesap bilgilerinizi görüntüleyin ve düzenleyin</p>
        </div>

        {/* Message */}
        {message && (
          <div className={`mb-8 p-4 rounded-xl border flex items-center gap-3 max-w-3xl mx-auto ${
            message.type === 'success'
              ? 'bg-green-500/10 border-green-500/30 text-green-400'
              : 'bg-red-500/10 border-red-500/30 text-red-400'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            {message.text}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {/* Profile Card */}
          <div className="bg-card text-card-foreground gap-6 rounded-xl border py-8 shadow-sm group flex flex-col transition-all duration-300 hover:shadow-lg hover:border-primary hover:-translate-y-1">
            <div className="px-8 flex flex-col">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-gradient-to-br from-[#478cbf] to-[#2d5aa0] rounded-lg text-white">
                    <User className="h-6 w-6" />
                  </div>
                  <h2 className="text-xl font-bold text-white">Profil Bilgileri</h2>
                </div>
                {!isEditing ? (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="flex items-center gap-2 px-3 py-2 bg-primary hover:bg-primary/80 text-primary-foreground rounded-lg transition-colors text-sm"
                  >
                    <Edit3 className="w-4 h-4" />
                    Düzenle
                  </button>
                ) : (
                  <div className="flex gap-2">
                    <button
                      onClick={handleSave}
                      disabled={isSaving}
                      className="flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white rounded-lg transition-colors text-sm"
                    >
                      <Save className="w-4 h-4" />
                      {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
                    </button>
                    <button
                      onClick={handleCancel}
                      className="flex items-center gap-2 px-3 py-2 bg-muted hover:bg-muted/80 text-muted-foreground rounded-lg transition-colors text-sm"
                    >
                      <X className="w-4 h-4" />
                      İptal
                    </button>
                  </div>
                )}
              </div>

              <div className="space-y-6">
                {/* Name */}
                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-2">
                    <User className="w-4 h-4 inline mr-2" />
                    Ad Soyad
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editForm.name}
                      onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                      className="w-full px-4 py-3 bg-neutral-800 border border-neutral-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                    />
                  ) : (
                    <p className="text-white text-lg">{userData.name}</p>
                  )}
                </div>

                {/* Username - Read Only */}
                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-2">
                    <User className="w-4 h-4 inline mr-2" />
                    Kullanıcı Adı
                  </label>
                  <p className="text-white text-lg">{userData.username}</p>
                  <p className="text-xs text-neutral-500 mt-1">Kullanıcı adı değiştirilemez</p>
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-2">
                    <Mail className="w-4 h-4 inline mr-2" />
                    E-posta
                  </label>
                  <p className="text-white text-lg">{userData.email}</p>
                  <p className="text-xs text-neutral-500 mt-1">E-posta adresi değiştirilemez</p>
                </div>

                {/* Phone - Read Only */}
                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-2">
                    <Phone className="w-4 h-4 inline mr-2" />
                    Telefon
                  </label>
                  <p className="text-white text-lg">{userData.phone || 'Belirtilmemiş'}</p>
                  <p className="text-xs text-neutral-500 mt-1">Telefon numarası değiştirilemez</p>
                </div>
              </div>
            </div>
          </div>

          {/* Account Status Card */}
          <div className="bg-card text-card-foreground gap-6 rounded-xl border py-8 shadow-sm group flex flex-col transition-all duration-300 hover:shadow-lg hover:border-primary hover:-translate-y-1">
            <div className="px-8 flex flex-col">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-gradient-to-br from-[#3b82f6] to-[#1d4ed8] rounded-lg text-white">
                  <Shield className="h-6 w-6" />
                </div>
                <h3 className="text-xl font-bold text-white">Hesap Durumu</h3>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-neutral-300">Doğrulama</span>
                  <span className={`flex items-center gap-2 ${
                    userData.isVerified ? 'text-green-400' : 'text-yellow-400'
                  }`}>
                    <CheckCircle className="w-4 h-4" />
                    {userData.isVerified ? 'Doğrulanmış' : 'Beklemede'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-neutral-300">Rol</span>
                  <span className="flex items-center gap-2 text-blue-400">
                    <Shield className="w-4 h-4" />
                    {userData.role === 'ADMIN' ? 'Admin' : 'Kullanıcı'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-neutral-300">Bakiye</span>
                  <span className="flex items-center gap-2 text-green-400">
                    <Wallet className="w-4 h-4" />
                    ₺{userData.balance.toFixed(2)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-neutral-300">Üyelik Tarihi</span>
                  <span className="flex items-center gap-2 text-neutral-400">
                    <Calendar className="w-4 h-4" />
                    {new Date(userData.createdAt).toLocaleDateString('tr-TR')}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions Card */}
          <div className="bg-card text-card-foreground gap-6 rounded-xl border py-8 shadow-sm group flex flex-col transition-all duration-300 hover:shadow-lg hover:border-primary hover:-translate-y-1">
            <div className="px-8 flex flex-col">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-gradient-to-br from-[#06b6d4] to-[#0ea5e9] rounded-lg text-white">
                  <Calendar className="h-6 w-6" />
                </div>
                <h3 className="text-xl font-bold text-white">Hızlı İşlemler</h3>
              </div>
              <div className="space-y-3">
                <Link
                  href="/settings"
                  className="block w-full px-4 py-3 bg-muted hover:bg-muted/80 text-white rounded-lg transition-colors text-center"
                >
                  Ayarlar
                </Link>
                <Link
                  href="/my-reports"
                  className="block w-full px-4 py-3 bg-muted hover:bg-muted/80 text-white rounded-lg transition-colors text-center"
                >
                  Raporlarım
                </Link>
                <Link
                  href="/support"
                  className="block w-full px-4 py-3 bg-muted hover:bg-muted/80 text-white rounded-lg transition-colors text-center"
                >
                  Destek
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
