'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeft,
  Zap,
  Users,
  Star,
  CheckCircle,
  AlertCircle,
  Gift,
  Crown,
  Rocket,
  Shield,
  Clock,
  Mail
} from 'lucide-react';

export default function BetaProgram() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);

  // Check enrollment status
  useEffect(() => {
    const checkEnrollment = () => {
      const enrolled = localStorage.getItem('betaEnrolled');
      setIsEnrolled(enrolled === 'true');
    };
    
    checkEnrollment();
  }, []);

  const handleEnroll = async () => {
    if (!session) {
      router.push('/');
      return;
    }

    setIsLoading(true);
    setMessage(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      localStorage.setItem('betaEnrolled', 'true');
      setIsEnrolled(true);
      setMessage({ 
        type: 'success', 
        text: 'Beta programına başarıyla katıldınız! Hoş geldiniz!' 
      });
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: 'Kayıt sırasında bir hata oluştu. Lütfen tekrar deneyin.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLeave = async () => {
    setIsLoading(true);
    setMessage(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      localStorage.removeItem('betaEnrolled');
      setIsEnrolled(false);
      setMessage({ 
        type: 'success', 
        text: 'Beta programından başarıyla ayrıldınız.' 
      });
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: 'İşlem sırasında bir hata oluştu.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const benefits = [
    {
      icon: Zap,
      title: 'Erken Erişim',
      description: 'Yeni özellikleri herkesten önce deneyin'
    },
    {
      icon: Gift,
      title: 'Özel İndirimler',
      description: 'Beta üyelere özel %20 indirim'
    },
    {
      icon: Crown,
      title: 'VIP Destek',
      description: 'Öncelikli müşteri desteği'
    },
    {
      icon: Users,
      title: 'Topluluk',
      description: 'Özel beta topluluğuna katılın'
    },
    {
      icon: Star,
      title: 'Geri Bildirim',
      description: 'Ürün geliştirme sürecine katkıda bulunun'
    },
    {
      icon: Shield,
      title: 'Beta Rozeti',
      description: 'Profilinizde özel beta rozeti'
    }
  ];

  const features = [
    'Yeni oyun motorları ve araçlar',
    'Gelişmiş web geliştirme özellikleri',
    'AI destekli kod önerileri',
    'Gelişmiş proje yönetimi araçları',
    'Özel Discord bot özellikleri',
    'FiveM için yeni script paketleri'
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16 max-w-3xl mx-auto">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-neutral-400 hover:text-white transition-colors mb-6 group"
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
            Ana Sayfaya Dön
          </Link>
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-4 bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl">
              <Rocket className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight text-white mb-4">
            Beta Program
          </h1>
          <p className="text-lg text-neutral-300">Geleceği bugünden deneyimleyin</p>
        </div>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg flex items-center gap-3 ${
            message.type === 'success' 
              ? 'bg-green-900/20 border border-green-800 text-green-400' 
              : 'bg-red-900/20 border border-red-800 text-red-400'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            {message.text}
          </div>
        )}

        {/* Status Card */}
        {isEnrolled && (
          <div className="mb-8 p-6 bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-800 rounded-xl">
            <div className="flex items-center gap-3 mb-3">
              <Crown className="w-6 h-6 text-purple-400" />
              <h2 className="text-xl font-semibold text-white">Beta Üyesi</h2>
            </div>
            <p className="text-purple-300 mb-4">
              Tebrikler! Beta programının aktif bir üyesisiniz. Yeni özellikleri keşfetmeye başlayabilirsiniz.
            </p>
            <div className="flex items-center gap-4 text-sm text-purple-400">
              <span className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Katılım: {new Date().toLocaleDateString('tr-TR')}
              </span>
              <span className="flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Beta güncellemeleri aktif
              </span>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Program Info */}
          <div className="space-y-6">
            <div className="bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
              <h2 className="text-2xl font-semibold text-white mb-4">Beta Program Nedir?</h2>
              <p className="text-neutral-300 mb-4">
                OnlyScript Beta Programı, yeni özellikleri ve güncellemeleri herkesten önce deneyimlemenizi sağlar. 
                Geri bildirimleriniz ile ürünlerimizi daha da iyileştiriyoruz.
              </p>
              <p className="text-neutral-300">
                Beta üyeleri olarak, geliştirme sürecinin bir parçası olur ve ürünlerimizin geleceğini şekillendirirsiniz.
              </p>
            </div>

            {/* Benefits */}
            <div className="bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Beta Üyesi Avantajları</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {benefits.map((benefit, index) => {
                  const IconComponent = benefit.icon;
                  return (
                    <div key={index} className="flex items-start gap-3">
                      <div className="p-2 bg-blue-600/20 rounded-lg">
                        <IconComponent className="w-5 h-5 text-blue-400" />
                      </div>
                      <div>
                        <h4 className="text-white font-medium">{benefit.title}</h4>
                        <p className="text-sm text-neutral-400">{benefit.description}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Enrollment */}
          <div className="space-y-6">
            {/* Action Card */}
            <div className="bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4">
                {isEnrolled ? 'Beta Üyeliği' : 'Beta Programına Katıl'}
              </h3>
              
              {!session ? (
                <div className="text-center py-8">
                  <p className="text-neutral-400 mb-4">Beta programına katılmak için giriş yapmanız gerekiyor.</p>
                  <button
                    onClick={() => router.push('/')}
                    className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    Giriş Yap
                  </button>
                </div>
              ) : (
                <div>
                  {isEnrolled ? (
                    <div>
                      <p className="text-neutral-300 mb-6">
                        Beta programının aktif bir üyesisiniz. İstediğiniz zaman programdan ayrılabilirsiniz.
                      </p>
                      <button
                        onClick={handleLeave}
                        disabled={isLoading}
                        className="w-full px-6 py-3 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded-lg transition-colors"
                      >
                        {isLoading ? 'İşleniyor...' : 'Beta Programından Ayrıl'}
                      </button>
                    </div>
                  ) : (
                    <div>
                      <p className="text-neutral-300 mb-6">
                        Beta programına katılarak yeni özellikleri ilk deneyenler arasında olun ve 
                        ürünlerimizin gelişimine katkıda bulunun.
                      </p>
                      <button
                        onClick={handleEnroll}
                        disabled={isLoading}
                        className="w-full px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors"
                      >
                        {isLoading ? 'Kaydediliyor...' : 'Beta Programına Katıl'}
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Upcoming Features */}
            <div className="bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Yakında Gelecek Özellikler</h3>
              <div className="space-y-3">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                    <span className="text-neutral-300">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Stats */}
            <div className="bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Program İstatistikleri</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">247</div>
                  <div className="text-sm text-neutral-400">Aktif Beta Üyesi</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">15</div>
                  <div className="text-sm text-neutral-400">Test Edilen Özellik</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">89%</div>
                  <div className="text-sm text-neutral-400">Memnuniyet Oranı</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-400">1.2k</div>
                  <div className="text-sm text-neutral-400">Geri Bildirim</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ */}
        <div className="mt-12 bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold text-white mb-6">Sık Sorulan Sorular</h3>
          <div className="space-y-4">
            <div>
              <h4 className="text-white font-medium mb-2">Beta programı ücretsiz mi?</h4>
              <p className="text-neutral-400">Evet, beta programına katılım tamamen ücretsizdir ve ek avantajlar sunar.</p>
            </div>
            <div>
              <h4 className="text-white font-medium mb-2">Beta özellikleri stabil mi?</h4>
              <p className="text-neutral-400">Beta özellikler test aşamasındadır ve bazen beklenmedik davranışlar sergileyebilir.</p>
            </div>
            <div>
              <h4 className="text-white font-medium mb-2">Geri bildirim nasıl verebilirim?</h4>
              <p className="text-neutral-400">Beta üyeleri özel Discord kanalımız üzerinden geri bildirim verebilir.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
