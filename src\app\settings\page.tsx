'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeft,
  Moon,
  Sun,
  Globe,
  Bell,
  Shield,
  Eye,
  EyeOff,
  Save,
  CheckCircle,
  AlertCircle,
  Smartphone,
  Monitor,
  Palette,
  Settings as SettingsIcon
} from 'lucide-react';

export default function Settings() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
  
  const [settings, setSettings] = useState({
    theme: 'dark',
    language: 'tr',
    notifications: {
      email: true,
      push: false,
      marketing: false
    },
    privacy: {
      profileVisible: true,
      showEmail: false,
      showPhone: false
    },
    display: {
      compactMode: false,
      animations: true,
      fontSize: 'medium'
    }
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
    }
  }, [status, router]);

  const handleSave = async () => {
    setIsLoading(true);
    setMessage(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Save to localStorage for now
      localStorage.setItem('userSettings', JSON.stringify(settings));
      
      setMessage({ type: 'success', text: 'Ayarlarınız başarıyla kaydedildi!' });
    } catch (error) {
      setMessage({ type: 'error', text: 'Ayarlar kaydedilirken bir hata oluştu.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }
  }, []);

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16 max-w-3xl mx-auto">
          <Link
            href="/user-panel"
            className="inline-flex items-center gap-2 text-neutral-400 hover:text-white transition-colors mb-6 group"
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
            Kullanıcı Paneline Dön
          </Link>
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight text-white mb-4">
            Ayarlar
          </h1>
          <p className="text-lg text-neutral-300">Hesap tercihlerinizi ve uygulama ayarlarınızı yönetin</p>
        </div>

        {/* Message */}
        {message && (
          <div className={`mb-8 p-4 rounded-xl border flex items-center gap-3 max-w-3xl mx-auto ${
            message.type === 'success'
              ? 'bg-green-500/10 border-green-500/30 text-green-400'
              : 'bg-red-500/10 border-red-500/30 text-red-400'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            {message.text}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {/* Appearance Settings */}
          <div className="bg-card text-card-foreground gap-6 rounded-xl border py-8 shadow-sm group flex flex-col transition-all duration-300 hover:shadow-lg hover:border-primary hover:-translate-y-1">
            <div className="px-8 flex flex-col">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-gradient-to-br from-[#478cbf] to-[#2d5aa0] rounded-lg text-white">
                  <Palette className="h-6 w-6" />
                </div>
                <h2 className="text-xl font-bold text-white">Görünüm</h2>
              </div>

              <div className="space-y-6">
              {/* Theme */}
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-3">Tema</label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={() => setSettings({...settings, theme: 'dark'})}
                    className={`flex items-center gap-3 p-3 rounded-lg border transition-colors ${
                      settings.theme === 'dark'
                        ? 'border-blue-500 bg-blue-500/10 text-blue-400'
                        : 'border-neutral-700 bg-neutral-800 text-neutral-300 hover:border-neutral-600'
                    }`}
                  >
                    <Moon className="w-5 h-5" />
                    Koyu
                  </button>
                  <button
                    onClick={() => setSettings({...settings, theme: 'light'})}
                    className={`flex items-center gap-3 p-3 rounded-lg border transition-colors ${
                      settings.theme === 'light'
                        ? 'border-blue-500 bg-blue-500/10 text-blue-400'
                        : 'border-neutral-700 bg-neutral-800 text-neutral-300 hover:border-neutral-600'
                    }`}
                  >
                    <Sun className="w-5 h-5" />
                    Açık
                  </button>
                </div>
              </div>

              {/* Language */}
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-3">Dil</label>
                <select
                  value={settings.language}
                  onChange={(e) => setSettings({...settings, language: e.target.value})}
                  className="w-full px-4 py-3 bg-neutral-800 border border-neutral-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="tr">Türkçe</option>
                  <option value="en">English</option>
                </select>
              </div>

              {/* Font Size */}
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-3">Yazı Boyutu</label>
                <select
                  value={settings.display.fontSize}
                  onChange={(e) => setSettings({
                    ...settings, 
                    display: {...settings.display, fontSize: e.target.value}
                  })}
                  className="w-full px-4 py-3 bg-neutral-800 border border-neutral-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="small">Küçük</option>
                  <option value="medium">Orta</option>
                  <option value="large">Büyük</option>
                </select>
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
              <Bell className="w-5 h-5" />
              Bildirimler
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white font-medium">E-posta Bildirimleri</p>
                  <p className="text-sm text-neutral-400">Önemli güncellemeler için e-posta alın</p>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    notifications: {...settings.notifications, email: !settings.notifications.email}
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.notifications.email ? 'bg-blue-600' : 'bg-neutral-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.notifications.email ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white font-medium">Push Bildirimleri</p>
                  <p className="text-sm text-neutral-400">Tarayıcı bildirimleri alın</p>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    notifications: {...settings.notifications, push: !settings.notifications.push}
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.notifications.push ? 'bg-blue-600' : 'bg-neutral-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.notifications.push ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white font-medium">Pazarlama E-postaları</p>
                  <p className="text-sm text-neutral-400">Yeni özellikler ve promosyonlar</p>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    notifications: {...settings.notifications, marketing: !settings.notifications.marketing}
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.notifications.marketing ? 'bg-blue-600' : 'bg-neutral-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.notifications.marketing ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Privacy Settings */}
          <div className="bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Gizlilik
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white font-medium">Profil Görünürlüğü</p>
                  <p className="text-sm text-neutral-400">Profilinizi diğer kullanıcılara göster</p>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    privacy: {...settings.privacy, profileVisible: !settings.privacy.profileVisible}
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.privacy.profileVisible ? 'bg-blue-600' : 'bg-neutral-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.privacy.profileVisible ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white font-medium">E-posta Görünürlüğü</p>
                  <p className="text-sm text-neutral-400">E-posta adresinizi göster</p>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    privacy: {...settings.privacy, showEmail: !settings.privacy.showEmail}
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.privacy.showEmail ? 'bg-blue-600' : 'bg-neutral-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.privacy.showEmail ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white font-medium">Telefon Görünürlüğü</p>
                  <p className="text-sm text-neutral-400">Telefon numaranızı göster</p>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    privacy: {...settings.privacy, showPhone: !settings.privacy.showPhone}
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.privacy.showPhone ? 'bg-blue-600' : 'bg-neutral-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.privacy.showPhone ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Display Settings */}
          <div className="bg-neutral-900/50 backdrop-blur-sm border border-neutral-800 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              Ekran
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white font-medium">Kompakt Mod</p>
                  <p className="text-sm text-neutral-400">Daha az boşluk ile sıkışık görünüm</p>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    display: {...settings.display, compactMode: !settings.display.compactMode}
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.display.compactMode ? 'bg-blue-600' : 'bg-neutral-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.display.compactMode ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white font-medium">Animasyonlar</p>
                  <p className="text-sm text-neutral-400">Geçiş animasyonlarını etkinleştir</p>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    display: {...settings.display, animations: !settings.display.animations}
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.display.animations ? 'bg-blue-600' : 'bg-neutral-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.display.animations ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="mt-8 flex justify-end">
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors"
          >
            <Save className="w-5 h-5" />
            {isLoading ? 'Kaydediliyor...' : 'Ayarları Kaydet'}
          </button>
        </div>
        </div>
      </div>
    </div>
  );
}
